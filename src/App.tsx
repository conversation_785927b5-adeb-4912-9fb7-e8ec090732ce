import ChatContainer from './components/Chat/ChatContainer'
import FloatingChatButton from './components/Chat/FloatingChatButton'
import QueryProvider from './providers/QueryProvider'
import { useChatStore } from './store'
import { VIEW_MODE } from './types'
import { useEffect } from 'react'

function App() {
  const { setViewMode, viewMode, unreadCount } = useChatStore();

  const handleRestoreFromMinimized = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };

  console.log({viewMode})

  // Apply CSS classes to body based on view mode
  useEffect(() => {
    const body = document.body;

    // Remove all chat-related classes
    body.classList.remove('chat-fullpage', 'chat-chatbox', 'chat-minimized');

    // Add the appropriate class based on current view mode
    switch (viewMode) {
      case VIEW_MODE.FULLPAGE:
        body.classList.add('chat-fullpage');
        break;
      case VIEW_MODE.CHATBOX:
        body.classList.add('chat-chatbox');
        break;
      case VIEW_MODE.MINIMIZED:
        body.classList.add('chat-minimized');
        break;
    }

    // Cleanup on unmount
    return () => {
      body.classList.remove('chat-fullpage', 'chat-chatbox', 'chat-minimized');
    };
  }, [viewMode]);

  return (
    <QueryProvider>
      <div className="app">
        {/* Background content to demonstrate scrolling */}
        <div style={{
          padding: '2rem',
          minHeight: '200vh',
          background: 'linear-gradient(45deg, #f0f9ff, #e0f2fe)',
          position: 'relative'
        }}>
          <h1 style={{ marginBottom: '2rem', color: '#1e40af' }}>Eneco Website Content</h1>

          {/* Test buttons for debugging */}
          <div style={{ marginBottom: '2rem', display: 'flex', gap: '1rem' }}>
            <button
              onClick={() => setViewMode(VIEW_MODE.FULLPAGE)}
              style={{ padding: '0.5rem 1rem', background: viewMode === VIEW_MODE.FULLPAGE ? '#1e40af' : '#e5e7eb', color: viewMode === VIEW_MODE.FULLPAGE ? 'white' : 'black' }}
            >
              Full Page
            </button>
            <button
              onClick={() => setViewMode(VIEW_MODE.CHATBOX)}
              style={{ padding: '0.5rem 1rem', background: viewMode === VIEW_MODE.CHATBOX ? '#1e40af' : '#e5e7eb', color: viewMode === VIEW_MODE.CHATBOX ? 'white' : 'black' }}
            >
              Chatbox
            </button>
            <button
              onClick={() => setViewMode(VIEW_MODE.MINIMIZED)}
              style={{ padding: '0.5rem 1rem', background: viewMode === VIEW_MODE.MINIMIZED ? '#1e40af' : '#e5e7eb', color: viewMode === VIEW_MODE.MINIMIZED ? 'white' : 'black' }}
            >
              Minimized
            </button>
            <span style={{ padding: '0.5rem', background: '#f3f4f6', borderRadius: '4px' }}>
              Current: {viewMode}
            </span>
          </div>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            This is sample content to demonstrate that the background is scrollable when the chat is in chatbox mode.
          </p>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            When the chat is in full page mode, this content should not be scrollable.
          </p>
          <p style={{ marginBottom: '1rem', lineHeight: '1.6' }}>
            When the chat is minimized to just the Edwin logo, this content should be fully scrollable.
          </p>

          {/* Add more content to make scrolling obvious */}
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} style={{
              margin: '2rem 0',
              padding: '1rem',
              background: 'white',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ marginBottom: '1rem', color: '#1e40af' }}>Section {i + 1}</h3>
              <p style={{ lineHeight: '1.6' }}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
                incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                 incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
              </p>
            </div>
          ))}
        </div>

        <ChatContainer />

        {/* Floating Chat Button - only visible when minimized */}
        <FloatingChatButton
          isVisible={viewMode === VIEW_MODE.MINIMIZED}
          onClick={handleRestoreFromMinimized}
          unreadCount={unreadCount}
        />
      </div>
    </QueryProvider>
  )
}

export default App
